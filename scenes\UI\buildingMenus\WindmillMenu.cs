using Godot;
using System;

public partial class WindmillMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;
	private Button _flourButton;
	private Windmill _currentWindmill;
	private ResourceType _selectedResource = ResourceType.Flour;
	private int _selectedAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_flourButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFlour/Button");

		_closeButton.Pressed += OnCloseButtonPressed;
		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;
		_flourButton.Pressed += OnFlourButtonPressed;

		var panel = GetNode<Sprite2D>("Control/Panel");
		if (panel != null) panel.Visible = false;
		if (_animationPlayer != null) _animationPlayer.Play("RESET");
		UpdateInfoBoard();
		MenuManager.Instance?.RegisterMenu("WindmillMenu", this);
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Sprite2D>("Control/Panel");
		return panel != null && panel.Visible;
	}

	public void SetWindmill(Windmill windmill)
	{
		_currentWindmill = windmill;
	}

	private void OnFlourButtonPressed()
	{
		_selectedResource = ResourceType.Flour;
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 1)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		if (_selectedAmount < maxAffordable)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Math.Max(1, maxAffordable / 4);
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = Math.Max(1, maxAffordable / 2);
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_selectedResource == ResourceType.None || _selectedAmount <= 0 || _currentWindmill == null)
			return;

		int maxAffordable = GetMaxAffordableAmount();
		if (maxAffordable < _selectedAmount)
		{
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		_currentWindmill.StartCrafting(_selectedResource, _selectedAmount);
		if (MenuManager.Instance != null)
			MenuManager.Instance.CloseMenu("WindmillMenu");
		else
			CloseMenu();
	}

	private void OnCloseButtonPressed()
	{
		GD.Print("test1");
		if (MenuManager.Instance != null)
			MenuManager.Instance.CloseMenu("WindmillMenu");
		else
			CloseMenu();
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None) return 0;
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return 0;
		if (_selectedResource == ResourceType.Flour)
		{
			int wheatCount = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wheat);
			return wheatCount / 2;
		}
		return 0;
	}

	private void UpdateInfoBoard()
	{
		if (_amountToProduce != null)
			_amountToProduce.Text = _selectedAmount.ToString();
		if (_itemFront != null && _selectedResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			if (texture != null)
				_itemFront.Texture = texture;
		}
	}

	public void OpenMenu(Windmill windmill)
	{
		_currentWindmill = windmill;
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);
		var panel = GetNode<Sprite2D>("Control/Panel");
		if (panel != null) panel.Visible = true;
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
			_animationPlayer.Play("Open");
		UpdateInfoBoard();
	}

	public void CloseMenu()
	{
		_currentWindmill = null;
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
		}
		else
		{
			var panel = GetNode<Sprite2D>("Control/Panel");
			if (panel != null) panel.Visible = false;
		}
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Sprite2D>("Control/Panel");
			if (panel != null) panel.Visible = false;
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	void IMenu.OpenMenu()
	{
		// Default implementation - requires windmill to be set externally
		if (_currentWindmill != null)
		{
			OpenMenu(_currentWindmill);
		}
	}
}
