using Godot;
using System;
using System.Collections.Generic;
using System.Resources;

public partial class Smoker : Node2D, IDestroyableObject, ICombatTarget
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Smoker;
	[Export] public int MaxHealth { get; set; } = 25;

	private const int BUILDING_WIDTH = 1;
	private const int BUILDING_HEIGHT = 2;
	private const int TILE_SIZE = 16;
	private const int MAX_HEALTH = 25;

	private int _currentHealth;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private string _saveId = "";

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _craftingResourceSprite;
	private ProgressBar _healthBar;
	private ProgressBarVertical _craftingProgressBar;
	private Area2D _playerDetector;
	private bool _isPlayerInRange = false;
	private Tween _hitTween;

	private ResourceType _selectedCraftingResource = ResourceType.None;
	private int _amountToProduce = 1;
	private Timer _craftingTimer;
	private bool _isCrafting = false;
	private int _currentCraftingProgress = 0;

	private readonly Dictionary<ResourceType, int> CraftingTimeSeconds = new()
	{
		{ ResourceType.SmokedFish, 10 }
	};

	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _hitColor = new Color(1.0f, 0.42f, 0.27f, 1.0f); // ff6c44
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f;

	private readonly Dictionary<ResourceType, Dictionary<ResourceType, int>> CraftingRecipes = new()
	{
		{ ResourceType.SmokedFish, new Dictionary<ResourceType, int> { { ResourceType.Fish, 3 } } }
	};

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite = GetNode<Sprite2D>("Smoker");
		_craftingResourceSprite = GetNode<Sprite2D>("CraftingResource");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		_playerDetector = GetNode<Area2D>("PlayerDetector");

		_craftingTimer = new Timer();
		_craftingTimer.WaitTime = 1.0f;
		_craftingTimer.OneShot = false;
		_craftingTimer.Timeout += OnCraftingTimerTimeout;
		AddChild(_craftingTimer);

		if (_healthBar != null)
		{
			_healthBar.Hide();
		}

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Hide();
		}

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}
		
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
		}

		UpdateCraftingResourceDisplay();
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event.IsActionPressed("Interact"))
		{
			OpenSmokerMenu();
		}
	}

	private void OnPlayerEntered(Area2D body)
	{
		if (body.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			CommonSignals.Instance?.EmitShowKeyEPrompt();
		}
	}

	private void OnPlayerExited(Area2D body)
	{
		if (body.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			CommonSignals.Instance?.EmitHideKeyEPrompt();
		}
	}

	private void OpenSmokerMenu()
	{
		var smokerMenu = GetNode<SmokerMenu>("SmokerMenu");
		if (smokerMenu != null)
		{
			smokerMenu.SetSmoker(this);

			// Use MenuManager to open the menu for proper ESC key handling
			if (MenuManager.Instance != null)
			{
				MenuManager.Instance.OpenMenu("SmokerMenu");
			}
			else
			{
				// Fallback to direct call if MenuManager is not available
				smokerMenu.OpenMenu(this);
			}
		}
	}

	public void SetTilePosition(Vector2I topLeftPosition)
	{
		_topLeftTilePosition = topLeftPosition;

		// For 1x2 building: center of 1 tile horizontally, center of 2 tiles vertically
		float centerX = (topLeftPosition.X + 0.5f) * TILE_SIZE;
		float centerY = (topLeftPosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void LoadFromSave(Vector2I topLeftPosition, CustomDataLayerManager customDataManager, int health)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;
		_currentHealth = health;
		_isPlaced = true;

		// Mark tiles as occupied when loading from save
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);
		UpdateHPBar();
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public void PlaceBuilding()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		if (!resourcesManager.HasResource(ResourceType.Plank, 18) ||
			!resourcesManager.HasResource(ResourceType.IronBar, 7))
		{
			return;
		}

		if (!resourcesManager.RemoveResource(ResourceType.Plank, 18) ||
			!resourcesManager.RemoveResource(ResourceType.IronBar, 7))
		{
			return;
		}

		PlaceBuilding(_topLeftTilePosition, _customDataManager);
	}

	public void PlaceBuilding(Vector2I topLeftPosition, CustomDataLayerManager customDataManager)
	{
		_topLeftTilePosition = topLeftPosition;
		_customDataManager = customDataManager;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftPosition.X + x, topLeftPosition.Y + y);
				customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		SetTilePosition(topLeftPosition);

		_isPlaced = true;

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Smoker", (int)BuildingType);
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public bool CanBePlacedAt(Vector2I topLeftPosition)
	{
		if (_customDataManager == null)
		{
			_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		}

		if (_customDataManager == null) return false;

		// Check if all tiles in the 1x2 area are available for building
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftPosition + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_sprite == null) return;

		// Green tint if can place, red if cannot
		_sprite.Modulate = canPlace ? new Color(0.8f, 1.0f, 0.8f, 0.8f) : new Color(1.0f, 0.8f, 0.8f, 0.8f);
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		UpdateHPBar();
		SaveBuildingState();

		PlayHitAnimation();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("hit"))
		{
			_animationPlayer.Play("hit");
		}

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	private void UpdateHPBar()
	{
		if (_healthBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;

		if (_currentHealth >= MaxHealth)
		{
			_healthBar.Hide();
		}
		else
		{
			_healthBar.Show();
			_healthBar.SetProgress(healthPercentage);
		}
	}

	private void DestroyBuilding()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		if (_customDataManager != null)
		{
			for (int x = 0; x < BUILDING_WIDTH; x++)
			{
				for (int y = 0; y < BUILDING_HEIGHT; y++)
				{
					Vector2I tilePos = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
					_customDataManager.ClearObjectPlaced(tilePos);
				}
			}

			// Save the updated custom layer data immediately
			var resourcesManager = ResourcesManager.Instance;
			if (resourcesManager != null)
			{
				resourcesManager.SaveCustomLayerData(_customDataManager);
			}
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		QueueFree();
	}

	public void StartCrafting(ResourceType resourceType, int amount)
	{
		if (_isCrafting) return;

		// Consume resources before starting crafting
		if (!ConsumeResources(resourceType, amount))
		{
			return;
		}

		_selectedCraftingResource = resourceType;
		_amountToProduce = amount;
		_currentCraftingProgress = 0;
		_isCrafting = true;

		UpdateCraftingResourceDisplay();
		_craftingTimer.Start();
	}

	private void OnCraftingTimerTimeout()
	{
		if (!_isCrafting || _selectedCraftingResource == ResourceType.None) return;

		_currentCraftingProgress++;
		UpdateCraftingProgress();

		if (CraftingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			if (_currentCraftingProgress >= requiredTime)
			{
				CompleteCrafting();
			}
		}
	}

	private void UpdateCraftingProgress()
	{
		if (_craftingProgressBar == null) return;

		if (CraftingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			float progress = (float)_currentCraftingProgress / requiredTime;
			_craftingProgressBar.SetProgress(progress);
			_craftingProgressBar.Show();

			if (_animationPlayer != null && _animationPlayer.HasAnimation("working"))
			{
				if (!_animationPlayer.IsPlaying() || _animationPlayer.CurrentAnimation != "working")
				{
					_animationPlayer.Play("working");
				}
			}
		}
	}

	private void UpdateCraftingResourceDisplay()
	{
		if (_craftingResourceSprite == null) return;

		if (_selectedCraftingResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedCraftingResource);
			_craftingResourceSprite.Texture = texture;
			_craftingResourceSprite.Visible = true;
		}
		else
		{
			_craftingResourceSprite.Visible = false;
		}
	}

	private void CompleteCrafting()
	{
		_craftingTimer.Stop();
		_isCrafting = false;

		SpawnCraftedItem();

		_animationPlayer.Play("RESET");

		_amountToProduce--;
		if (_amountToProduce > 0)
		{
			_currentCraftingProgress = 0;
			_isCrafting = true;
			_craftingTimer.Start();
		}
		else
		{
			_selectedCraftingResource = ResourceType.None;
			_currentCraftingProgress = 0;
			UpdateCraftingResourceDisplay();
			_craftingProgressBar?.Hide();
		}
	}

	private void SpawnCraftedItem()
	{
		Vector2 spawnPosition = GlobalPosition + new Vector2(
			GD.RandRange(-10, 10),
			GD.RandRange(-10, 10)
		);
		DroppedResource.SpawnResource(spawnPosition, _selectedCraftingResource, 1);
	}

	public bool CanCraft(ResourceType resourceType, int amount)
	{
		if (!CraftingRecipes.TryGetValue(resourceType, out var recipe))
			return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			int required = ingredient.Value * amount;
			if (!resourcesManager.HasResource(ingredient.Key, required))
				return false;
		}

		return true;
	}

	public bool ConsumeResources(ResourceType resourceType, int amount)
	{
		if (!CraftingRecipes.TryGetValue(resourceType, out var recipe))
			return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			int required = ingredient.Value * amount;
			if (!resourcesManager.HasResource(ingredient.Key, required))
				return false;
		}

		foreach (var ingredient in recipe)
		{
			int required = ingredient.Value * amount;
			resourcesManager.RemoveResource(ingredient.Key, required);
		}

		return true;
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				if (buildingTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeRepairedFrom(playerTile))
					{
						RepairBuilding(repairAmount);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			Vector2 playerPos = player.GlobalPosition;
			return new Vector2I((int)playerPos.X / 16, (int)playerPos.Y / 16);
		}
		return Vector2I.Zero;
	}

	public bool CanBeRepairedFrom(Vector2I playerTile)
	{
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I buildingTile = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				Vector2I distance = buildingTile - playerTile;
				if (Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1)
				{
					return true;
				}
			}
		}
		return false;
	}

	public bool CanBeHitFrom(Vector2I playerTile)
	{
		return CanBeRepairedFrom(playerTile);
	}

	private void RepairBuilding(int repairAmount)
	{
		if (_currentHealth >= MaxHealth) return;

		_currentHealth = Math.Min(_currentHealth + repairAmount, MaxHealth);
		UpdateHPBar();
		SaveBuildingState();
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	private void SaveBuildingState()
	{
		if (string.IsNullOrEmpty(_saveId)) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var buildings = resourcesManager.GetBuildings();
		var buildingData = buildings.Find(b => b.Id == _saveId);
		if (buildingData != null)
		{
			buildingData.CurrentHealth = _currentHealth;
			buildingData.SelectedCraftingResource = (int)_selectedCraftingResource;
			buildingData.CraftingProgress = _currentCraftingProgress;
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
		}
	}

	// ICombatTarget implementation
	public TargetType GetTargetType()
	{
		return TargetType.ProductionBuilding;
	}

	public bool CanBeTargeted()
	{
		return _isPlaced && !_isBeingDestroyed && _currentHealth > 0;
	}

	public Vector2 GetTargetPosition()
	{
		return GlobalPosition;
	}

	public void OnTargeted(Node2D enemy)
	{
		// Smoker doesn't need special behavior when targeted
	}

	public void OnAttacked(int damage, EnemyType attackerType)
	{
		// Use existing damage system
		TakeDamage(damage);
	}
}
