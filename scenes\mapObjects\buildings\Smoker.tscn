[gd_scene load_steps=10 format=3 uid="uid://smoker001"]

[ext_resource type="Script" path="res://scenes/mapObjects/buildings/Smoker.cs" id="1_smoker"]
[ext_resource type="Texture2D" path="res://resources/solaria/buildings/smoker.png" id="2_smoker"]
[ext_resource type="PackedScene" uid="uid://bkxvn4h4qnxbr" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_smoker"]
[ext_resource type="PackedScene" uid="uid://cqhpnqxnqxnqx" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="4_smoker"]
[ext_resource type="PackedScene" uid="uid://smokermenu001" path="res://scenes/UI/buildingMenus/SmokerMenu.tscn" id="5_smoker"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_smoker"]
size = Vector2(16, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_smoker_detector"]
size = Vector2(48, 48)

[sub_resource type="Animation" id="Animation_smoker_hit"]
resource_name = "hit"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Smoker:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_smoker_working"]
resource_name = "working"
length = 1.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Smoker:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5, 1.0),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_smoker"]
_data = {
&"hit": SubResource("Animation_smoker_hit"),
&"working": SubResource("Animation_smoker_working")
}

[node name="Smoker" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_smoker")
BuildingType = 32

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_smoker")
}

[node name="Smoker" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_smoker")

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -8)
scale = Vector2(0.5, 0.5)

[node name="StaticBody2D" type="StaticBody2D" parent="."]
collision_layer = 2
collision_mask = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
shape = SubResource("RectangleShape2D_smoker")

[node name="ProgressBar" parent="." instance=ExtResource("3_smoker")]
position = Vector2(0, -20)

[node name="ProgressBarVertical" parent="." instance=ExtResource("4_smoker")]
position = Vector2(12, 0)

[node name="PlayerDetector" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
shape = SubResource("RectangleShape2D_smoker_detector")

[node name="SmokerMenu" parent="." instance=ExtResource("5_smoker")]
