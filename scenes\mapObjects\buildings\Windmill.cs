using Godot;
using System;
using System.Collections.Generic;

public partial class Windmill : Node2D, IDestroyableObject, ICombatTarget
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Windmill;
	[Export] public int MaxHealth { get; set; } = 30;

	// Task 50 - Windmill dimensions: 4x5 visual, 4x2 base
	private const int BUILDING_WIDTH = 4;
	private const int BUILDING_HEIGHT = 5; // Base height for collision
	private const int VISUAL_HEIGHT = 5; // Full visual height
	private const int TILE_SIZE = 16;
	private const int MAX_HEALTH = 30;

	private int _currentHealth;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private string _saveId = "";

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _craftingResourceSprite;
	private ProgressBar _healthBar;
	private ProgressBarVertical _craftingProgressBar;
	private Area2D _playerDetector;
	private bool _isPlayerInRange = false;
	private Tween _hitTween;
	private WindmillMenu _windmillMenu;

	private ResourceType _selectedCraftingResource = ResourceType.Flour;
	private int _amountToProduce = 1;
	private Timer _craftingTimer;
	private bool _isCrafting = false;
	private int _currentCraftingProgress = 0;

	// Task 50 - Windmill recipe: 2x Wheat -> 1x Flour
	private readonly Dictionary<ResourceType, int> CraftingTimeSeconds = new()
	{
		{ ResourceType.Flour, 8 } // 8 seconds to process wheat into flour
	};

	private readonly Dictionary<ResourceType, Dictionary<ResourceType, int>> CraftingRecipes = new()
	{
		{
			ResourceType.Flour, new Dictionary<ResourceType, int>
			{
				{ ResourceType.Wheat, 2 } // 2 wheat -> 1 flour
			}
		}
	};

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_craftingResourceSprite = GetNode<Sprite2D>("CraftingResourceSprite");

		if (_healthBar != null)
		{
			_healthBar.Hide();
		}

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Hide();
		}

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed += OnHammerUsed;
		}

		_craftingTimer = new Timer();
		_craftingTimer.WaitTime = 1.0;
		_craftingTimer.Timeout += OnCraftingTimerTimeout;
		AddChild(_craftingTimer);

		// Get WindmillMenu from the windmill scene
		_windmillMenu = GetNode<WindmillMenu>("WindmillMenu");
		if (_windmillMenu != null)
		{
			// WindmillMenu will register itself with MenuManager in its _Ready method
		}
		else
		{
			GD.PrintErr("Windmill: WindmillMenu not found in windmill scene!");
		}

		// Task 50 - Set proper positioning for 4x5 building with 4x2 base
		// The sprite should be positioned so the bottom part occupies the 4x2 base
		if (_sprite != null)
		{
			// Offset the sprite up so the bottom 2 tiles align with the base
			_sprite.Position = new Vector2(BUILDING_WIDTH * TILE_SIZE / 2, -((VISUAL_HEIGHT - BUILDING_HEIGHT) * TILE_SIZE));
			GetNode<StaticBody2D>("StaticBody2D").Position = new Vector2(BUILDING_WIDTH * TILE_SIZE / 2, -((VISUAL_HEIGHT - BUILDING_HEIGHT) * TILE_SIZE));
			GetNode<Area2D>("PlayerDetector").Position = new Vector2(BUILDING_WIDTH * TILE_SIZE / 2, -((VISUAL_HEIGHT - BUILDING_HEIGHT) * TILE_SIZE));
		}

		UpdateCraftingResourceDisplay();
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event.IsActionPressed("Interact"))
		{
			OpenWindmillMenu();
		}
	}

	private void OpenWindmillMenu()
	{
		var windmillMenu = GetNode<WindmillMenu>("WindmillMenu");
		if (windmillMenu != null)
		{
			windmillMenu.SetWindmill(this);

			// Use MenuManager to open the menu for proper ESC key handling
			if (MenuManager.Instance != null)
			{
				MenuManager.Instance.OpenMenu("WindmillMenu");
			}
			else
			{
				// Fallback to direct call if MenuManager is not available
				windmillMenu.OpenMenu(this);
			}
		}
		else
		{
			GD.PrintErr("Windmill: WindmillMenu not found in windmill scene!");
		}
	}

	public void StartCrafting(ResourceType resourceType, int amount)
	{
		if (_isCrafting)
		{
			GD.Print("Windmill: Already crafting!");
			return;
		}

		if (!CraftingRecipes.ContainsKey(resourceType))
		{
			GD.Print($"Windmill: No recipe for {resourceType}");
			return;
		}

		var recipe = CraftingRecipes[resourceType];
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
		{
			GD.PrintErr("Windmill: ResourcesManager not found!");
			return;
		}

		// Check if player has required resources
		foreach (var ingredient in recipe)
		{
			int requiredAmount = ingredient.Value * amount;
			if (!resourcesManager.HasResource(ingredient.Key, requiredAmount))
			{
				GD.Print($"Windmill: Not enough {ingredient.Key}. Need {requiredAmount}");
				return;
			}
		}

		// Consume resources
		foreach (var ingredient in recipe)
		{
			int requiredAmount = ingredient.Value * amount;
			resourcesManager.RemoveResource(ingredient.Key, requiredAmount);
		}

		_selectedCraftingResource = resourceType;
		_amountToProduce = amount;
		_isCrafting = true;
		_currentCraftingProgress = 0;

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Show();
			_craftingProgressBar.SetProgress(0f);
		}

		if (_craftingTimer != null)
		{
			_craftingTimer.Start();
		}

		UpdateCraftingResourceDisplay();
		GD.Print($"Windmill: Started crafting {amount}x {resourceType}");
	}

	private void OnCraftingTimerTimeout()
	{
		if (!_isCrafting) return;

		_currentCraftingProgress++;

		int totalCraftingTime = CraftingTimeSeconds[_selectedCraftingResource] * _amountToProduce;

		if (_craftingProgressBar != null)
		{
			float progress = (float)_currentCraftingProgress / totalCraftingTime;
			_craftingProgressBar.SetProgress(progress);
		}

		if (_currentCraftingProgress >= totalCraftingTime)
		{
			CompleteCrafting();
		}
	}

	private void CompleteCrafting()
	{
		_isCrafting = false;
		_craftingTimer?.Stop();

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Hide();
		}

		// Spawn the crafted resource
		for (int i = 0; i < _amountToProduce; i++)
		{
			Vector2 spawnPosition = GlobalPosition + new Vector2(
				GD.RandRange(-20, 20),
				GD.RandRange(-20, 20)
			);
			DroppedResource.SpawnResource(spawnPosition, _selectedCraftingResource, 1);
		}

		UpdateCraftingResourceDisplay();
		GD.Print($"Windmill: Completed crafting {_amountToProduce}x {_selectedCraftingResource}");

		_selectedCraftingResource = ResourceType.Flour;
		_amountToProduce = 1;
	}

	private void UpdateCraftingResourceDisplay()
	{
		if (_craftingResourceSprite == null) return;

		if (_isCrafting && _selectedCraftingResource != ResourceType.None)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedCraftingResource);
			if (texture != null)
			{
				_craftingResourceSprite.Texture = texture;
				_craftingResourceSprite.Show();
			}
		}
		else
		{
			_craftingResourceSprite.Hide();
		}
	}

	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null)
		{
			_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		}

		if (_customDataManager == null)
		{
			return false;
		}

		// Check if all tiles in the 4x5 area are available
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftTile.X + x, topLeftTile.Y + y);
				var tileData = _customDataManager.GetTileData(tilePos);
				if (!tileData.CanBuilding || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;

		// For 4x5 building: center of 4 tiles horizontally, center of 5 tiles vertically
		float centerX = (topLeftTile.X + 2.0f) * TILE_SIZE;
		float centerY = (topLeftTile.Y + 2.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_sprite != null)
		{
			_sprite.Modulate = canPlace ? Colors.White : Colors.Red;
		}
	}

	public void PlaceBuilding()
	{
		if (_customDataManager == null)
		{
			_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		}

		if (_customDataManager == null)
		{
			GD.PrintErr("Windmill: CustomDataLayerManager not found!");
			return;
		}

		// Mark tiles as occupied in the 4x5 area
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		// Consume resources for building
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.RemoveResource(ResourceType.Plank, BuildMenu.WINDMILL_PLANK_REQUIRED);
			resourcesManager.RemoveResource(ResourceType.IronBar, BuildMenu.WINDMILL_IRONBAR_REQUIRED);
		}

		_isPlaced = true;

		// Reset sprite color
		if (_sprite != null)
		{
			_sprite.Modulate = Colors.White;
		}

		// Save building data
		SaveBuildingData();

		GD.Print($"Windmill: Building placed at {_topLeftTilePosition}");
	}

	private void SaveBuildingData()
	{
		if (string.IsNullOrEmpty(_saveId))
		{
			_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Windmill", (int)BuildingType);
		}
	}

	public void LoadFromSave(Vector2I topLeftTile, CustomDataLayerManager customDataManager, int health)
	{
		_topLeftTilePosition = topLeftTile;
		_customDataManager = customDataManager;
		_currentHealth = health;
		_isPlaced = true;

		GlobalPosition = new Vector2(topLeftTile.X * TILE_SIZE, topLeftTile.Y * TILE_SIZE);

		UpdateHPBar();

		// Mark tiles as occupied
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = new Vector2I(topLeftTile.X + x, topLeftTile.Y + y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		GD.Print($"Windmill: Loaded from save at {topLeftTile} with {health} health");
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	// IDestroyableObject implementation
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		_currentHealth = Mathf.Max(0, _currentHealth);

		UpdateHPBar();

		// Hit animation
		if (_hitTween != null)
		{
			_hitTween.Kill();
		}
		_hitTween = CreateTween();
		_hitTween.TweenProperty(_sprite, "modulate", new Color(1.0f, 0.42f, 0.27f), 0.1);
		_hitTween.TweenProperty(_sprite, "modulate", Colors.White, 0.1);

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
		else
		{
			// Hide health bar after 3 seconds
			var hideTimer = GetTree().CreateTimer(3.0);
			hideTimer.Timeout += () => _healthBar?.Hide();
		}

		// Update save data
		SaveBuildingData();
	}

	private void UpdateHPBar()
	{
		if (_healthBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;

		if (_currentHealth >= MaxHealth)
		{
			_healthBar.Hide();
		}
		else
		{
			_healthBar.Show();
			_healthBar.SetProgress(healthPercentage);
		}
	}

	public void Heal(int healAmount)
	{
		if (_isBeingDestroyed) return;

		_currentHealth += healAmount;
		_currentHealth = Mathf.Min(MaxHealth, _currentHealth);

		UpdateHPBar();

		if (_currentHealth < MaxHealth)
		{
			var hideTimer = GetTree().CreateTimer(3.0);
			hideTimer.Timeout += () => _healthBar?.Hide();
		}

		SaveBuildingData();
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public int GetMaxHealth()
	{
		return MaxHealth;
	}

	public void DestroyBuilding()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Free tiles
		if (_customDataManager != null)
		{
			for (int x = 0; x < BUILDING_WIDTH; x++)
			{
				for (int y = 0; y < BUILDING_HEIGHT; y++)
				{
					Vector2I tilePos = new Vector2I(_topLeftTilePosition.X + x, _topLeftTilePosition.Y + y);
					_customDataManager.ClearObjectPlaced(tilePos);
				}
			}
		}

		// Remove from save data
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null && !string.IsNullOrEmpty(_saveId))
		{
			resourcesManager.RemoveBuildingById(_saveId);
		}

		// Stop crafting
		_isCrafting = false;
		_craftingTimer?.Stop();

		QueueFree();
		GD.Print("Windmill: Building destroyed");
	}

	// IDestroyableObject implementation - missing methods
	public bool CanBeHitFrom(Vector2I attackerTilePosition)
	{
		// Allow hits from any direction
		return true;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
	}

	// ICombatTarget implementation
	public new Vector2 GetGlobalPosition()
	{
		return GlobalPosition;
	}

	public bool IsValidTarget()
	{
		return !_isBeingDestroyed;
	}

	public TargetType GetTargetType()
	{
		return TargetType.ProductionBuilding;
	}

	public bool CanBeTargeted()
	{
		return !_isBeingDestroyed;
	}

	public Vector2 GetTargetPosition()
	{
		return GlobalPosition;
	}

	public void OnTargeted(Node2D attacker)
	{
		// Building doesn't need special targeting behavior
	}

	public void OnAttacked(int damage, EnemyType enemyType)
	{
		TakeDamage(damage);
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			CommonSignals.Instance?.EmitShowKeyEPrompt();
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			CommonSignals.Instance?.EmitHideKeyEPrompt();
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private void OnHammerUsed(Vector2I tilePosition, int repairAmount)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						Heal(repairAmount);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			Vector2 playerPos = player.GlobalPosition;
			return new Vector2I((int)playerPos.X / 16, (int)playerPos.Y / 16);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
			CommonSignals.Instance.HammerUsed -= OnHammerUsed;
		}
	}

	// Task 50 - Semi-transparency when player is behind
	public override void _Process(double delta)
	{
		if (_sprite == null) return;

		var player = GetNode<Node2D>("/root/world/Player");
		if (player != null)
		{
			// Check if player is behind the windmill (higher Y position)
			if (player.GlobalPosition.Y < GlobalPosition.Y)
			{
				_sprite.Modulate = new Color(1, 1, 1, 0.6f); // Semi-transparent
			}
			else
			{
				_sprite.Modulate = Colors.White; // Fully opaque
			}
		}
	}
}
