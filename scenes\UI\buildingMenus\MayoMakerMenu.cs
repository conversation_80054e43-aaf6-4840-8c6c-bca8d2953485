using Godot;
using System;

public partial class MayoMakerMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Control _control;
	private Sprite2D _panel;
	private Button _closeButton;
	private Button _mayoButton;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;
	private Label _amountToProduce;
	private Sprite2D _itemFront;

	private MayoMaker _mayoMaker;
	private ResourceType _selectedResource = ResourceType.Mayo;
	private int _currentAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_control = GetNode<Control>("Control");
		_panel = GetNode<Sprite2D>("Control/Panel");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_mayoButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo/Button");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");

		_closeButton.Pressed += OnCloseButtonPressed;
		_mayoButton.Pressed += OnMayoButtonPressed;
		_buttonMinusOne.Pressed += OnMinusOnePressed;
		_buttonPlusOne.Pressed += OnPlusOnePressed;
		_buttonSetOne.Pressed += OnSetOnePressed;
		_buttonSet25Percent.Pressed += OnSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnSet50PercentPressed;
		_buttonSetMax.Pressed += OnSetMaxPressed;
		_buttonProduce.Pressed += OnProducePressed;

		_panel.Visible = false;

		UpdateSelectedResource();
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Escape") && _panel.Visible)
		{
			CloseMenu();
		}
	}

	public void SetMayoMaker(MayoMaker mayoMaker)
	{
		_mayoMaker = mayoMaker;
	}

	public void OpenMenu(MayoMaker mayoMaker)
	{
		_mayoMaker = mayoMaker;
		_selectedResource = ResourceType.Mayo;
		_currentAmount = 1;
		UpdateSelectedResource();
		UpdateAmountDisplay();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}
		else
		{
			_panel.Visible = true;
		}
	}

	private void CloseMenu()
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}
		else
		{
			_panel.Visible = false;
		}

		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("MayoMakerMenu");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnMayoButtonPressed()
	{
		_selectedResource = ResourceType.Mayo;
		UpdateSelectedResource();
	}

	private void OnMinusOnePressed()
	{
		_currentAmount = Math.Max(1, _currentAmount - 1);
		UpdateAmountDisplay();
	}

	private void OnPlusOnePressed()
	{
		_currentAmount++;
		UpdateAmountDisplay();
	}

	private void OnSetOnePressed()
	{
		_currentAmount = 1;
		UpdateAmountDisplay();
	}

	private void OnSet25PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 4);
		UpdateAmountDisplay();
	}

	private void OnSet50PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 2);
		UpdateAmountDisplay();
	}

	private void OnSetMaxPressed()
	{
		_currentAmount = GetMaxCraftableAmount();
		UpdateAmountDisplay();
	}

	private void OnProducePressed()
	{
		if (_mayoMaker != null && _mayoMaker.CanCraft(_selectedResource, _currentAmount))
		{
			_mayoMaker.StartCrafting(_selectedResource, _currentAmount);
			CloseMenu();
		}
	}

	private void UpdateSelectedResource()
	{
		if (_itemFront != null)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
		}
	}

	private void UpdateAmountDisplay()
	{
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _currentAmount.ToString();
		}
	}

	private int GetMaxCraftableAmount()
	{
		if (_mayoMaker == null) return 1;

		// Mayo requires 2 eggs
		int eggAvailable = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Egg);
		int maxAmount = Math.Max(1, eggAvailable / 2);

		return maxAmount;
	}
}
